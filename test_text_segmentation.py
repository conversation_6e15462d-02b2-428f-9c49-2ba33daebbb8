#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文本分段功能
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'audiobooks_monolith'))

from app.services.audio_conversion_service import AudioConversionService

def create_test_text():
    """创建测试文本"""
    # 创建一个约5000字的测试文本，更接近实际使用场景
    paragraphs = [
        "这是第一个段落，讲述了一个关于人工智能发展的故事。在这个故事中，我们看到了技术如何改变人类的生活方式，以及人们如何适应这些变化。" * 15,  # 约900字
        "第二个段落继续探讨人工智能在各个领域的应用。从医疗健康到教育培训，从交通运输到金融服务，人工智能正在深刻地影响着我们生活的方方面面。" * 12,  # 约840字
        "这是一个相对较短的段落。" * 8,  # 约96字
        "第三个段落详细分析了人工智能技术的发展趋势和未来展望。随着深度学习、机器学习等技术的不断进步，我们有理由相信人工智能将在未来发挥更加重要的作用。" * 18,  # 约1260字
        "短段落测试。" * 3,  # 约18字
        "最后一个段落总结了全文的主要观点！我们需要以开放的心态拥抱人工智能技术？同时也要注意其可能带来的挑战和风险！只有这样，我们才能真正从中受益。" * 20,  # 约1200字
    ]

    return "\n\n".join(paragraphs)

def test_text_segmentation():
    """测试文本分段功能"""
    print("开始测试文本分段功能...")
    
    # 创建服务实例
    service = AudioConversionService()
    
    # 创建测试文本
    test_text = create_test_text()
    print(f"测试文本总长度: {len(test_text)} 字符")
    print(f"测试文本段落数: {len(test_text.split('\\n\\n'))}")
    
    # 执行分段
    segments = service._split_text_into_segments(test_text)
    
    print(f"\\n分段结果:")
    print(f"总段落数: {len(segments)}")
    
    for i, segment in enumerate(segments):
        print(f"段落 {i+1}: {len(segment)} 字符")
        print(f"内容预览: {segment[:100]}...")
        print("-" * 50)
    
    # 验证分段结果
    print("\\n验证结果:")
    
    # 检查是否有段落超过1200字符（允许一些缓冲）
    long_segments = [i for i, seg in enumerate(segments) if len(seg) > 1200]
    if long_segments:
        print(f"警告: 发现过长段落 (>1200字符): {long_segments}")
    else:
        print("✓ 所有段落长度都在合理范围内")
    
    # 检查是否有段落过短（<20字符）
    short_segments = [i for i, seg in enumerate(segments) if len(seg) < 20]
    if short_segments:
        print(f"警告: 发现过短段落 (<20字符): {short_segments}")
    else:
        print("✓ 没有过短段落")
    
    # 检查平均段落长度
    avg_length = sum(len(seg) for seg in segments) / len(segments)
    print(f"平均段落长度: {avg_length:.0f} 字符")
    
    if 800 <= avg_length <= 1200:
        print("✓ 平均段落长度符合预期 (800-1200字符)")
    else:
        print("⚠ 平均段落长度可能需要调整")
    
    return segments

def test_short_text():
    """测试短文本处理"""
    print("\\n测试短文本处理...")
    
    service = AudioConversionService()
    
    # 测试短文本（<800字符）
    short_text = "这是一个短文本。" * 30  # 约240字符
    segments = service._split_text_into_segments(short_text)
    
    print(f"短文本长度: {len(short_text)} 字符")
    print(f"分段结果: {len(segments)} 个段落")
    
    if len(segments) == 1:
        print("✓ 短文本正确处理，未进行分段")
    else:
        print("⚠ 短文本处理可能有问题")

def test_sentence_boundary():
    """测试句子边界识别"""
    print("\\n测试句子边界识别...")
    
    service = AudioConversionService()
    
    # 创建包含各种标点符号的长文本
    text_with_punctuation = (
        "这是第一句话。这是第二句话！这是第三句话？" +
        "这里有一个邮箱地址****************，不应该在这里分割。" +
        "这里有一个网址http://www.example.com，也不应该分割。" +
        "这是正常的句子结尾。" +
        "还有更多内容继续测试分段逻辑的准确性。"
    ) * 50  # 重复50次，确保超过1000字符
    
    segments = service._split_text_into_segments(text_with_punctuation)
    
    print(f"测试文本长度: {len(text_with_punctuation)} 字符")
    print(f"分段结果: {len(segments)} 个段落")
    
    # 检查是否在句子中间分割
    for i, segment in enumerate(segments):
        if not segment.strip().endswith(('。', '！', '？', '.', '!', '?')):
            print(f"⚠ 段落 {i+1} 可能在句子中间分割: ...{segment[-50:]}")
        else:
            print(f"✓ 段落 {i+1} 在句子边界正确分割")

if __name__ == "__main__":
    print("=" * 60)
    print("文本分段功能测试")
    print("=" * 60)
    
    try:
        # 测试主要分段功能
        segments = test_text_segmentation()
        
        # 测试短文本处理
        test_short_text()
        
        # 测试句子边界识别
        test_sentence_boundary()
        
        print("\\n" + "=" * 60)
        print("测试完成")
        print("=" * 60)
        
    except Exception as e:
        print(f"测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
